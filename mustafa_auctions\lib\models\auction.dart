class Auction {
  final int id;
  final String title;
  final String imageUrl;
  final double currentBid;
  final int bidCount;
  final DateTime endTime;
  final bool featured;
  final String category;
  final String description;

  Auction({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.currentBid,
    required this.bidCount,
    required this.endTime,
    this.featured = false,
    required this.category,
    required this.description,
  });

  // Calculate time remaining
  Duration get timeRemaining {
    final now = DateTime.now();
    if (endTime.isAfter(now)) {
      return endTime.difference(now);
    }
    return Duration.zero;
  }

  // Format time remaining as string
  String get timeRemainingFormatted {
    final remaining = timeRemaining;
    final days = remaining.inDays;
    final hours = remaining.inHours % 24;
    final minutes = remaining.inMinutes % 60;
    final seconds = remaining.inSeconds % 60;

    return "${days.toString().padLeft(2, '0')} أيام\n${hours.toString().padLeft(2, '0')} ساعة\n${minutes.toString().padLeft(2, '0')} دقيقة\n${seconds.toString().padLeft(2, '0')} ثانية";
  }

  // Format current bid as currency
  String get formattedBid {
    return "\$${currentBid.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}";
  }
}