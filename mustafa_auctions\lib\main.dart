import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'models/auction.dart';
import 'services/auction_service.dart';
import 'widgets/auction_card.dart';

void main() {
  runApp(const MustafaAuctionsApp());
}

class MustafaAuctionsApp extends StatelessWidget {
  const MustafaAuctionsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مصطفى مختار - المزادات الفاخرة',
      theme: ThemeData(
        primarySwatch: Colors.amber,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        fontFamily: 'Helvetica',
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1A1A1A),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
      ),
      home: const AuctionHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AuctionHomePage extends StatefulWidget {
  const AuctionHomePage({super.key});

  @override
  State<AuctionHomePage> createState() => _AuctionHomePageState();
}

class _AuctionHomePageState extends State<AuctionHomePage> {
  List<Auction> auctions = [];

  @override
  void initState() {
    super.initState();
    loadAuctions();
  }

  void loadAuctions() {
    setState(() {
      auctions = AuctionService.getSampleAuctions();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F0F),
      appBar: AppBar(
        title: const Text(
          'مصطفى مختار - المزادات الفاخرة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section
            Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1A1A1A),
                    Color(0xFF0F0F0F),
                  ],
                ),
              ),
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Text(
                    'مزادات حصرية وفاخرة',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'بيت مزادات راقي',
                    style: TextStyle(
                      fontSize: 18,
                      color: Color(0xFFB8860B),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'اكتشف القطع الحصرية وزايد على السيارات الفاخرة والتحف والمقتنيات',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFFCCCCCC),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            // Section Title
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: const Text(
                'جميع المزادات الفاخرة',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            // Auctions Grid
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: auctions.map((auction) => 
                  AuctionCard(auction: auction)
                ).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}